class User {
  final String name;
  final String address;
  final String phone;

  User({required this.name, required this.address, required this.phone});

  factory User.fromJson(Map<String, dynamic> json) {
    return User(
      name: json['name'] ?? '',
      address:
          "${json['address']['street']}, ${json['address']['city']}", // address is nested
      phone: json['phone'] ?? '',
    );
  }
}
