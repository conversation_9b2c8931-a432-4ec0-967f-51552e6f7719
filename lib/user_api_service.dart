import 'package:dio/dio.dart';

import 'user.dart';

class UserApiService {
  final Dio dio;

  UserApiService(this.dio);

  Future<List<User>> getAllUsers() async {
    final res = await dio.get('/users');
    final data = res.data as List;
    return data.map((json) => User.fromJson(json)).toList();
  }

  Future<User> getUserById(int id) async {
    final res = await dio.get('/users/$id');
    return res.data;
  }
}
