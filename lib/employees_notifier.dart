import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:riverpod_tutorial/employees.dart';

part 'employees_notifier.g.dart';

@riverpod
class EmployeesNotifier extends _$EmployeesNotifier {
  late SortType _sortType;
  SortType get sortType => _sortType;

  void sortBy(SortType sortType) {
    _sortType = sortType;
    final sortedlist = [...state];
    sortedlist.sort((a, b) {
      switch (sortType) {
        case SortType.name:
          return a.name.compareTo(b.name);
        case SortType.age:
          return a.age.compareTo(b.age);
        case SortType.experience:
          return a.experience.compareTo(b.experience);
      }
    });
    state = sortedlist;
  }

  @override
  List<Employee> build() {
    _sortType = SortType.name;
    return [
      Employee(
        name: '<PERSON><PERSON>',
        age: 25,
        phone: '0773547295',
        email: '<EMAIL>',
        experience: 3,
      ),
      Employee(
        name: '<PERSON><PERSON><PERSON>',
        age: 26,
        phone: '0559389427',
        email: 'hammou.chem<PERSON><PERSON><PERSON>@gmail.com',
        experience: 2,
      ),
      Employee(
        name: '<PERSON><PERSON>',
        age: 27,
        phone: '0669696969',
        email: '<EMAIL>',
        experience: 5,
      ),
      Employee(
        name: 'Oussama Belkacem',
        age: 28,
        phone: '0779368257',
        email: '<EMAIL>',
        experience: 4,
      ),
      Employee(
        name: 'Abdou Lehoua',
        age: 29,
        phone: '0698674923',
        email: '<EMAIL>',
        experience: 1,
      ),
      Employee(
        name: 'Imene Allou',
        age: 30,
        phone: '0987654321',
        email: '<EMAIL>',
        experience: 3,
      ),
      Employee(
        name: 'Sara Arbati',
        age: 31,
        phone: '0798496382',
        email: '<EMAIL>',
        experience: 7,
      ),
      Employee(
        name: 'Kadeur Zawali',
        age: 32,
        phone: '0587295479',
        email: '<EMAIL>',
        experience: 4,
      ),
      Employee(
        name: 'Lyes Meftahi',
        age: 26,
        phone: '0661029384',
        email: '<EMAIL>',
        experience: 2,
      ),
      Employee(
        name: 'Nadia Boukhari',
        age: 27,
        phone: '0771234567',
        email: '<EMAIL>',
        experience: 5,
      ),
      Employee(
        name: 'Amine Zerguine',
        age: 28,
        phone: '0658439201',
        email: '<EMAIL>',
        experience: 3,
      ),
      Employee(
        name: 'Yasmine Hattab',
        age: 29,
        phone: '0784567890',
        email: '<EMAIL>',
        experience: 6,
      ),
      Employee(
        name: 'Farouk Dali',
        age: 30,
        phone: '0550923847',
        email: '<EMAIL>',
        experience: 4,
      ),
      Employee(
        name: 'Souad Maamer',
        age: 31,
        phone: '0678921345',
        email: '<EMAIL>',
        experience: 7,
      ),
      Employee(
        name: 'Tarek Boussa',
        age: 32,
        phone: '0567382940',
        email: '<EMAIL>',
        experience: 2,
      ),
      Employee(
        name: 'Linda Lahlou',
        age: 28,
        phone: '0693847562',
        email: '<EMAIL>',
        experience: 5,
      ),
      Employee(
        name: 'Rami Sifi',
        age: 27,
        phone: '0638927451',
        email: '<EMAIL>',
        experience: 1,
      ),
      Employee(
        name: 'Salima Benamara',
        age: 30,
        phone: '0772849391',
        email: '<EMAIL>',
        experience: 6,
      ),
      Employee(
        name: 'Ikram Dendene',
        age: 29,
        phone: '0701122334',
        email: '<EMAIL>',
        experience: 3,
      ),
      Employee(
        name: 'Walid Benslimane',
        age: 31,
        phone: '0612345678',
        email: '<EMAIL>',
        experience: 4,
      ),
    ];
  }
}
