// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'employees_notifier.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$employeesNotifierHash() => r'8d6b59fba8e9ab17bc03cc1f2ab9001d2c164800';

/// See also [EmployeesNotifier].
@ProviderFor(EmployeesNotifier)
final employeesNotifierProvider =
    AutoDisposeNotifierProvider<EmployeesNotifier, List<Employee>>.internal(
      EmployeesNotifier.new,
      name: r'employeesNotifierProvider',
      debugGetCreateSourceHash:
          const bool.fromEnvironment('dart.vm.product')
              ? null
              : _$employeesNotifierHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$EmployeesNotifier = AutoDisposeNotifier<List<Employee>>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
