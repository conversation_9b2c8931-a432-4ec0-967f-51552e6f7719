class Employee {
  String name;
  int age;
  String phone;
  String email;
  int experience;

  Employee({
    required this.name,
    required this.age,
    required this.phone,
    required this.email,
    required this.experience,
  });
}

enum SortType { name, age, experience }

List<Employee> employees = [
  Employee(
    name: '<PERSON><PERSON>',
    age: 25,
    phone: '0773547295',
    email: 'na<PERSON><PERSON><PERSON>@gmail.com',
    experience: 3,
  ),
  Employee(
    name: '<PERSON><PERSON><PERSON>',
    age: 26,
    phone: '0559389427',
    email: 'hammouche<PERSON><PERSON><PERSON>@gmail.com',
    experience: 2,
  ),
  Employee(
    name: '<PERSON><PERSON>',
    age: 27,
    phone: '0669696969',
    email: '<EMAIL>',
    experience: 5,
  ),
  Employee(
    name: '<PERSON><PERSON><PERSON> Binladen',
    age: 28,
    phone: '0779368257',
    email: '<EMAIL>',
    experience: 4,
  ),
  Employee(
    name: '<PERSON><PERSON>',
    age: 29,
    phone: '0698674923',
    email: 'ab<PERSON><PERSON><PERSON><PERSON>@gmail.com',
    experience: 1,
  ),
  Employee(
    name: '<PERSON><PERSON><PERSON>',
    age: 30,
    phone: '0987654321',
    email: '<PERSON><PERSON><EMAIL>',
    experience: 3,
  ),
  Employee(
    name: 'Sara Arbati',
    age: 31,
    phone: '0798496382',
    email: '<EMAIL>',
    experience: 7,
  ),
  Employee(
    name: 'Kadeur Zawali',
    age: 32,
    phone: '0587295479',
    email: '<EMAIL>',
    experience: 4,
  ),
];
