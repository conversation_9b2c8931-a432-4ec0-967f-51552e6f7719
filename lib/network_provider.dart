import 'package:dio/dio.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:riverpod_tutorial/user.dart';

import 'user_api_service.dart';

part 'network_provider.g.dart';

@riverpod
Dio dio(ref) {
  final dio = Dio(
    BaseOptions(
      baseUrl: 'https://jsonplaceholder.typicode.com', // change it to your API
      connectTimeout: const Duration(seconds: 10),
      receiveTimeout: const Duration(seconds: 10),
      headers: {'Content-Type': 'application/json'},
    ),
  );

  // You can add interceptors here if needed
  dio.interceptors.add(LogInterceptor(responseBody: true));

  return dio;
}

@riverpod
UserApiService userApiService(ref) {
  final dioInstance = ref.watch(dioProvider);
  return UserApiService(dioInstance);
}

// final userApiServiceProvider = Provider<UserApiService>((ref) {
//   final dio = ref.read(dioProvider);
//   return UserApiService(dio);
// });
@riverpod
Future<List<User>> users(ref) {
  final api = ref.watch(userApiServiceProvider);
  return api.getAllUsers();
}
