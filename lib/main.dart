import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_tutorial/employees_notifier.dart';
import 'package:riverpod_tutorial/user_list_page.dart';

import 'employees.dart';

void main() {
  runApp(const ProviderScope(child: MainApp()));
}

class MainApp extends StatelessWidget {
  const MainApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      debugShowCheckedModeBanner: false,
      // Optional: Define a custom theme for consistent typography and colors
      theme: ThemeData(
        useMaterial3: false, // Disable Material 3 for more control
        textTheme: const TextTheme(
          titleLarge: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: Colors.black87,
          ),
          bodyMedium: TextStyle(fontSize: 14, color: Colors.black54),
        ),
      ),
      home: const UserListPage(),
    );
  }
}

class HomePage extends ConsumerWidget {
  const HomePage({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final sortedEmployees = ref.watch(employeesNotifierProvider);
    SortType selectedSortType =
        ref.watch(employeesNotifierProvider.notifier).sortType;

    return Scaffold(
      floatingActionButton: DropdownButton<SortType>(
        value: selectedSortType,
        onChanged: (SortType? value) {
          selectedSortType = value!;
          ref.watch(employeesNotifierProvider.notifier).sortBy(value);
        },
        items: const [
          DropdownMenuItem(value: SortType.age, child: Text("Age")),
          DropdownMenuItem(value: SortType.name, child: Text("Name")),
          DropdownMenuItem(
            value: SortType.experience,
            child: Text("Experience"),
          ),
        ],
        style: const TextStyle(color: Colors.black87, fontSize: 16),
        dropdownColor: Colors.white,
        elevation: 4,
        borderRadius: BorderRadius.circular(12),
      ),
      appBar: AppBar(
        title: const Text('Riverpod Tutorial', style: TextStyle(fontSize: 20)),
        backgroundColor: Colors.blueAccent,
        elevation: 0,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: ListView.builder(
          itemCount: sortedEmployees.length,
          itemBuilder: (context, index) {
            final employee = sortedEmployees[index];
            return Padding(
              padding: const EdgeInsets.symmetric(vertical: 8.0),
              child: GestureDetector(
                onTap: () {},
                child: Card(
                  elevation: 6,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(16),
                  ),
                  child: Container(
                    decoration: BoxDecoration(
                      gradient: const LinearGradient(
                        colors: [Colors.blueAccent, Colors.lightBlue],
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                      ),
                      borderRadius: BorderRadius.circular(16),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withValues(alpha: 0.1),
                          blurRadius: 10,
                          offset: const Offset(0, 4),
                        ),
                      ],
                    ),
                    child: ListTile(
                      contentPadding: const EdgeInsets.all(16),
                      leading: CircleAvatar(
                        backgroundColor: Colors.white,
                        child: Text(
                          employee.name[0].toUpperCase(),
                          style: const TextStyle(
                            color: Colors.blueAccent,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                      title: Text(
                        employee.name,
                        style: Theme.of(context).textTheme.titleLarge,
                      ),
                      subtitle: Text(
                        'Age: ${employee.age} | Exp: ${employee.experience} yrs',
                        style: TextStyle(fontSize: 18),
                      ),
                    ),
                  ),
                ),
              ),
            );
          },
        ),
      ),
    );
  }
}
